/**
 * Error tracking service
 * Provides utilities for tracking and reporting errors
 */

// Configuration
const config = {
  enabled: process.env.NODE_ENV === 'production',
  sampleRate: 1.0, // 100% of errors
  maxErrors: 100, // Maximum number of errors to store locally
  reportUrl: process.env.REACT_APP_ERROR_REPORTING_URL || '/api/errors',
  applicationName: 'app-builder-201',
  applicationVersion: process.env.REACT_APP_VERSION || '1.0.0',
};

// Error storage
let errorStorage = {
  errors: [],
  lastReportTime: 0,
};

// Load error storage from localStorage
try {
  const storedErrors = localStorage.getItem('errorStorage');
  if (storedErrors) {
    errorStorage = JSON.parse(storedErrors);
  }
} catch (error) {
  console.error('Failed to load error storage:', error);
}

/**
 * Save error storage to localStorage
 */
const saveErrorStorage = () => {
  try {
    localStorage.setItem('errorStorage', JSON.stringify(errorStorage));
  } catch (error) {
    console.error('Failed to save error storage:', error);
  }
};

/**
 * Get browser and environment information
 * @returns {Object} Browser and environment information
 */
const getEnvironmentInfo = () => {
  return {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight,
    url: window.location.href,
    referrer: document.referrer,
    timestamp: new Date().toISOString(),
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    applicationName: config.applicationName,
    applicationVersion: config.applicationVersion,
    environment: process.env.NODE_ENV,
  };
};

/**
 * Serialize error object
 * @param {Error} error - Error object
 * @returns {Object} Serialized error
 */
const serializeError = (error) => {
  if (!error) return null;
  
  return {
    name: error.name,
    message: error.message,
    stack: error.stack,
    fileName: error.fileName,
    lineNumber: error.lineNumber,
    columnNumber: error.columnNumber,
    cause: error.cause ? serializeError(error.cause) : null,
  };
};

/**
 * Track error
 * @param {Error} error - Error object
 * @param {Object} additionalInfo - Additional information about the error
 * @returns {string} Error ID
 */
export const trackError = (error, additionalInfo = {}) => {
  if (!config.enabled) return null;
  
  // Sample errors based on sample rate
  if (Math.random() > config.sampleRate) return null;
  
  // Generate error ID
  const errorId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Create error report
  const errorReport = {
    id: errorId,
    timestamp: Date.now(),
    error: serializeError(error),
    environment: getEnvironmentInfo(),
    additionalInfo,
  };
  
  // Add error to storage
  errorStorage.errors.push(errorReport);
  
  // Limit the number of stored errors
  if (errorStorage.errors.length > config.maxErrors) {
    errorStorage.errors = errorStorage.errors.slice(-config.maxErrors);
  }
  
  // Save error storage
  saveErrorStorage();
  
  // Report error
  reportErrors();
  
  return errorId;
};

import csrfService from './csrfService';

/**
 * Report errors to the server
 * @returns {Promise<void>}
 */
export const reportErrors = async () => {
  if (!config.enabled || errorStorage.errors.length === 0) return;
  
  // Check if we should report errors (at most once per minute)
  const now = Date.now();
  if (now - errorStorage.lastReportTime < 60000) return;
  
  try {
    // Update last report time
    errorStorage.lastReportTime = now;
    saveErrorStorage();
    
    // Get CSRF headers
    let headers = {
      'Content-Type': 'application/json',
    };
    
    try {
      const csrfHeaders = await csrfService.getHeaders();
      headers = { ...headers, ...csrfHeaders };
    } catch (error) {
      console.warn('Failed to get CSRF token for error reporting:', error);
    }
    
    // Send errors to the server
    const response = await fetch(config.reportUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        errors: errorStorage.errors,
      }),
      credentials: 'include', // Include cookies for CSRF
      // Don't report errors from reporting errors
      keepalive: true,
    });
    
    if (response.ok) {
      // Clear reported errors
      errorStorage.errors = [];
      saveErrorStorage();
    }
  } catch (error) {
    console.error('Failed to report errors:', error);
  }
};

/**
 * Get all tracked errors
 * @returns {Array} Tracked errors
 */
export const getTrackedErrors = () => {
  return [...errorStorage.errors];
};

/**
 * Clear all tracked errors
 */
export const clearTrackedErrors = () => {
  errorStorage.errors = [];
  saveErrorStorage();
};

/**
 * Initialize error tracking
 */
export const initErrorTracking = () => {
  if (!config.enabled) return;
  
  // Track unhandled errors
  window.addEventListener('error', (event) => {
    trackError(event.error || new Error(event.message), {
      type: 'unhandled',
      source: event.filename,
      line: event.lineno,
      column: event.colno,
    });
  });
  
  // Track unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    trackError(event.reason, {
      type: 'unhandledrejection',
    });
  });
  
  // Report errors before page unload
  window.addEventListener('beforeunload', () => {
    reportErrors();
  });
  
  // Report errors periodically
  setInterval(reportErrors, 60000);
};

export default {
  trackError,
  reportErrors,
  getTrackedErrors,
  clearTrackedErrors,
  initErrorTracking,
};

