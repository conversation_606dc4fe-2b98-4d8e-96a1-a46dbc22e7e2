/**
 * Enhanced Service Worker for App Builder 201
 *
 * This service worker provides offline support, caching, and push notifications
 * for the App Builder application.
 */

// Development mode detection
const isDevelopment = self.location.hostname === 'localhost' ||
  self.location.hostname === '127.0.0.1' ||
  self.location.port === '3000';

// Automatic cache versioning
const CACHE_VERSION = isDevelopment ? 'dev-' + Date.now() : '1.0.0';
const CACHE_NAME = `app-builder-cache-v${CACHE_VERSION}`;
const OFFLINE_URL = '/offline.html';

// Cache configuration
const CACHE_CONFIG = {
  maxAge: isDevelopment ? 1000 * 60 * 5 : 1000 * 60 * 60 * 24, // 5 min dev, 24 hours prod
  maxEntries: 100
};

// Assets to cache - updated to match actual build output
const ASSETS_TO_CACHE = [
  '/',
  '/index.html',
  '/offline.html',
  '/static/css/main.css',
  '/static/js/main.bundle.js',
  '/static/js/bundle.js',
  '/static/js/shared.bundle.js',
  '/static/js/runtime-shared.bundle.js',
  '/manifest.json',
  '/favicon.ico',
  '/logo.svg',
  '/api-offline.json'
];

// Install event - cache assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Install');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching assets');
        return cache.addAll(ASSETS_TO_CACHE);
      })
      .then(() => {
        console.log('[Service Worker] Installed');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[Service Worker] Install error:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activate');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('[Service Worker] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] Activated');
        return self.clients.claim();
      })
      .catch((error) => {
        console.error('[Service Worker] Activate error:', error);
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip browser-sync requests
  if (event.request.url.includes('browser-sync')) {
    return;
  }

  // Skip WebSocket requests and Webpack HMR/SockJS requests
  if (
    event.request.url.startsWith('ws:') ||
    event.request.url.startsWith('wss:') ||
    event.request.url.includes('/ws/') ||
    event.request.url.includes('/sockjs-node') ||
    event.request.url.match(/hot-update\.(js|json)$/)
  ) {
    return;
  }

  // Skip API requests - let them go through the normal proxy
  if (event.request.url.includes('/api/')) {
    // Don't intercept API requests, let the main thread handle them
    // This allows the webpack dev server proxy to work correctly
    return;
  }

  // Handle navigation requests (SPA routes)
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch((err) => {
          console.error('[Service Worker] Navigation fetch failed:', event.request.url, err);
          return caches.match(OFFLINE_URL);
        })
    );
    return;
  }

  // Handle other requests with stale-while-revalidate strategy
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // Revalidate the cache in the background
          fetch(event.request)
            .then((response) => {
              if (response.ok) {
                const responseForCache = response.clone();
                caches.open(CACHE_NAME).then((cache) => {
                  cache.put(event.request, responseForCache);
                });
                // Response cached successfully in background
              }
            })
            .catch((err) => {
              // Network request failed, but we already returned the cached response
              console.error('[Service Worker] Background fetch failed:', event.request.url, err);
            });

          return cachedResponse;
        }

        // No cached response, try network
        return fetch(event.request)
          .then((response) => {
            if (response.ok) {
              const responseForCache = response.clone();
              caches.open(CACHE_NAME).then((cache) => {
                cache.put(event.request, responseForCache);
              });
              // Response cached successfully
            }

            return response;
          })
          .catch((err) => {
            // Network request failed, try to return a cached fallback
            console.error('[Service Worker] Network fetch failed:', event.request.url, err);
            if (event.request.headers.get('accept').includes('text/html')) {
              return caches.match(OFFLINE_URL);
            }

            if (event.request.headers.get('accept').includes('image/')) {
              return caches.match('/logo.svg');
            }

            return new Response('Offline content not available', {
              status: 503,
              statusText: 'Service Unavailable',
              headers: new Headers({
                'Content-Type': 'text/plain'
              })
            });
          });
      })
  );
});

// Push event - handle push notifications
self.addEventListener('push', (event) => {
  console.log('[Service Worker] Push received');

  const data = event.data.json();

  const options = {
    body: data.body,
    icon: '/logo.svg',
    badge: '/logo.svg',
    data: {
      url: data.url
    }
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click event - handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('[Service Worker] Notification click');

  event.notification.close();

  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  );
});

// Message event - handle messages from the client
self.addEventListener('message', (event) => {
  console.log('[Service Worker] Message received:', event.data);

  if (event.data.action === 'skipWaiting') {
    self.skipWaiting();
  }
});
