/**
 * Quick Verification Script
 * 
 * This script can be run in the browser console to quickly verify
 * that all the service worker and API fixes are working correctly.
 * 
 * Usage: Copy and paste this entire script into the browser console
 */

(async function quickVerification() {
  console.log('🚀 Starting Quick Verification of App Builder Fixes...');
  console.log('='.repeat(60));
  
  const results = {
    serviceWorker: false,
    apiEndpoints: false,
    authentication: false,
    overall: false
  };
  
  let testsPassed = 0;
  let testsFailed = 0;
  
  function logResult(test, passed, message) {
    const icon = passed ? '✅' : '❌';
    const status = passed ? 'PASS' : 'FAIL';
    console.log(`${icon} ${test}: ${status} - ${message}`);
    
    if (passed) {
      testsPassed++;
    } else {
      testsFailed++;
    }
  }
  
  // Test 1: Service Worker Registration
  console.log('\n1️⃣ Testing Service Worker...');
  try {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration && registration.active) {
        results.serviceWorker = true;
        logResult('Service Worker Registration', true, 'Active and registered');
        console.log(`   Scope: ${registration.scope}`);
        console.log(`   Script: ${registration.active.scriptURL}`);
      } else {
        logResult('Service Worker Registration', false, 'Not active or not registered');
      }
    } else {
      logResult('Service Worker Support', false, 'Not supported in this browser');
    }
  } catch (error) {
    logResult('Service Worker Registration', false, `Error: ${error.message}`);
  }
  
  // Test 2: API Endpoints
  console.log('\n2️⃣ Testing API Endpoints...');
  const apiTests = [
    { url: '/api/status/', name: 'Status API' },
    { url: '/api/health/', name: 'Health API' },
    { url: '/api/csrf-token/', name: 'CSRF Token API' }
  ];
  
  let apiSuccess = true;
  let authSuccess = true;
  
  for (const test of apiTests) {
    try {
      const startTime = performance.now();
      const response = await fetch(test.url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      if (response.ok) {
        const data = await response.json();
        logResult(test.name, true, `Status ${response.status} (${responseTime}ms)`);
        console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
      } else if (response.status === 401) {
        logResult(test.name, false, `Authentication error (401) - This was the original problem!`);
        apiSuccess = false;
        authSuccess = false;
      } else {
        logResult(test.name, false, `Status ${response.status}`);
        apiSuccess = false;
      }
    } catch (error) {
      logResult(test.name, false, `Network error: ${error.message}`);
      apiSuccess = false;
    }
  }
  
  results.apiEndpoints = apiSuccess;
  results.authentication = authSuccess;
  
  // Test 3: Service Worker Non-Interference
  console.log('\n3️⃣ Testing Service Worker Non-Interference...');
  try {
    // Test that API calls are not being intercepted by service worker
    const testResponse = await fetch('/api/status/', {
      method: 'GET',
      headers: {
        'X-Test-Header': 'service-worker-interference-test',
        'Content-Type': 'application/json'
      }
    });
    
    if (testResponse.ok) {
      logResult('Service Worker Non-Interference', true, 'API calls are not being intercepted');
    } else {
      logResult('Service Worker Non-Interference', false, `API call failed with status ${testResponse.status}`);
    }
  } catch (error) {
    logResult('Service Worker Non-Interference', false, `Test failed: ${error.message}`);
  }
  
  // Test 4: App Builder Application
  console.log('\n4️⃣ Testing App Builder Application...');
  try {
    // Check if we're on the App Builder page
    const currentPath = window.location.pathname;
    const isAppBuilderPage = currentPath === '/' || currentPath.includes('app-builder') || currentPath.includes('mvp');
    
    if (isAppBuilderPage) {
      logResult('App Builder Page', true, 'Currently on App Builder page');
    } else {
      logResult('App Builder Page', true, `On page: ${currentPath}`);
    }
    
    // Check if React is loaded
    if (typeof React !== 'undefined') {
      logResult('React Framework', true, 'React is loaded and available');
    } else {
      logResult('React Framework', false, 'React not detected');
    }
    
    // Check if the main app container exists
    const appContainer = document.getElementById('root') || document.querySelector('[data-reactroot]');
    if (appContainer) {
      logResult('App Container', true, 'React app container found');
    } else {
      logResult('App Container', false, 'React app container not found');
    }
    
  } catch (error) {
    logResult('App Builder Application', false, `Error: ${error.message}`);
  }
  
  // Overall Results
  console.log('\n📊 VERIFICATION RESULTS');
  console.log('='.repeat(60));
  
  const overallSuccess = results.serviceWorker && results.apiEndpoints && results.authentication;
  results.overall = overallSuccess;
  
  console.log(`Service Worker: ${results.serviceWorker ? '✅ WORKING' : '❌ ISSUES'}`);
  console.log(`API Endpoints: ${results.apiEndpoints ? '✅ WORKING' : '❌ ISSUES'}`);
  console.log(`Authentication: ${results.authentication ? '✅ FIXED' : '❌ STILL BROKEN'}`);
  console.log(`Tests Passed: ${testsPassed}`);
  console.log(`Tests Failed: ${testsFailed}`);
  
  if (overallSuccess) {
    console.log('\n🎉 SUCCESS! All fixes are working correctly!');
    console.log('✅ Service worker is registered but not interfering with API calls');
    console.log('✅ API endpoints are accessible and returning proper responses');
    console.log('✅ No authentication errors (401) are occurring');
    console.log('✅ Your App Builder application should be fully functional!');
    
    // Show success notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('App Builder Verification', {
        body: 'All systems are working correctly! 🎉',
        icon: '/logo192.png'
      });
    }
  } else {
    console.log('\n⚠️ Some issues detected:');
    if (!results.serviceWorker) {
      console.log('❌ Service Worker issues detected');
    }
    if (!results.apiEndpoints) {
      console.log('❌ API endpoint issues detected');
    }
    if (!results.authentication) {
      console.log('❌ Authentication issues still present (401 errors)');
    }
    
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Check browser console for any error messages');
    console.log('2. Verify backend server is running on port 8000');
    console.log('3. Verify frontend server is running on port 3000');
    console.log('4. Check network tab for failed requests');
    console.log('5. Try refreshing the page to reload the service worker');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Open the main App Builder application: http://localhost:3000');
  console.log('2. Test creating and editing components');
  console.log('3. Verify WebSocket connections are working');
  console.log('4. Check that data persistence is functioning');
  
  return results;
})();

// Also make the function available globally for manual testing
window.quickVerification = quickVerification;
