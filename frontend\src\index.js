import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from './redux/store';
import App from './App';
import { initializeAllServices } from './utils/initializeServices';
import { initServiceWorkerCleanup } from './utils/serviceWorkerCleanup';
import { ThemeProvider } from './components/theme/ThemeManager';
import { initMockApiServer } from './utils/mockApiServer';
import { initMockWebSocketServer } from './utils/mockWebSocketServer';
import { initPerformanceOptimizer } from './utils/performanceOptimizer';
import connectionManager from './services/ConnectionManager';
import apiClient from './services/ApiClient';
import webSocketClient from './services/WebSocketClient';
window.fetch = (function (originalFetch) {
  return function (url, options) {
    try {
      // Your mock API logic here
      // ...

      // Example:
      if (url === '/api/some-endpoint') {
        return Promise.resolve({
          json: () => Promise.resolve({ data: 'mock data' }),
          ok: true,
          status: 200,
        });
      }

      // If no mock is found, call the original fetch
      return originalFetch.call(window, url, options);
    } catch (error) {
      console.error('Error in mockApiServer.js:', error);
      // Return a rejected promise or a default response
      return Promise.reject(error); // Or return a default response
    }
  };
})(window.fetch);
// Import global theme styles
import './styles/globalTheme.css';
import './styles/theme.css';

// Expose React to global scope for debugging and verification
window.React = React;
window.ReactDOM = { createRoot };

// Add debugging information
console.log('🚀 React loaded successfully:', React.version);
console.log('🔧 React available globally:', typeof window.React);
console.log('🏗️ ReactDOM createRoot available:', typeof createRoot);

// Add global styles directly to avoid CSS import issues
const style = document.createElement('style');
style.textContent = `
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);

// Import error tracker
import errorTracker from './utils/errorTracker';

// Initialize error tracker with custom configuration
errorTracker.updateConfig({
  enabled: true,
  samplingRate: 1.0,
  reportingEndpoint: '/api/errors',
  logToConsole: true
});

// Add global error handler to log detailed information
window.addEventListener('error', (event) => {
  console.error('Global error caught:', {
    message: event.message,
    source: event.filename,
    lineNo: event.lineno,
    colNo: event.colno,
    error: event.error
  });

  // Error is already tracked by the error tracker
});

// Add unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);

  // Error is already tracked by the error tracker
});

// Initialize service worker cleanup
initServiceWorkerCleanup();

// Initialize mock servers in development mode
// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development';

// Initialize mock API server
initMockApiServer({
  enabled: isDev,
  delay: 300,
  logRequests: true
});
console.log(`Mock API server ${isDev ? 'initialized' : 'disabled'} for testing`);

// Initialize mock WebSocket server
initMockWebSocketServer({
  enabled: isDev,
  logMessages: true
});
console.log(`Mock WebSocket server ${isDev ? 'initialized' : 'disabled'} for testing`);

// Add a global flag to indicate mock servers are enabled
window.MOCK_SERVERS_ENABLED = isDev;

// Register the service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js')
      .then((registration) => {
        console.log('Service Worker registered successfully:', registration.scope);

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New Service Worker installed and ready');
              // Optionally notify user about update
              if (window.confirm('New version available! Reload to update?')) {
                window.location.reload();
              }
            }
          });
        });
      })
      .catch((error) => {
        console.error('Service Worker registration failed:', error);
      });
  });
}

// Initialize performance optimizer
initPerformanceOptimizer({
  lazyLoadImages: true,
  resourceHints: true
});
console.log('Performance optimizer initialized');

// Initialize connection manager
connectionManager.init();
console.log('Connection manager initialized');

// Initialize API client
apiClient.initServices().catch(error => {
  console.warn('API client initialization error:', error);
});

// Initialize WebSocket client
webSocketClient.initServices().catch(error => {
  console.warn('WebSocket client initialization error:', error);
});

// Initialize all services
console.log('Initializing all services...');
initializeAllServices()
  .then(() => {
    console.log('All services initialized successfully');

    // Show success message
    const message = document.createElement('div');
    message.textContent = 'Services initialized successfully!';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = 'var(--color-success, #52c41a)';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.boxShadow = 'var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15))';
    message.style.zIndex = '1000';
    document.body.appendChild(message);

    // Remove message after 3 seconds
    setTimeout(() => {
      document.body.removeChild(message);
    }, 3000);
  })
  .catch(error => {
    console.error('Error initializing services:', error);

    // Show error message
    const message = document.createElement('div');
    message.textContent = 'Error initializing services. Check the console for details.';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = 'var(--color-error, #f5222d)';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.boxShadow = 'var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15))';
    message.style.zIndex = '1000';
    document.body.appendChild(message);

    // Remove message after 5 seconds
    setTimeout(() => {
      document.body.removeChild(message);
    }, 5000);
  });

// Import the enhanced error boundary
import EnhancedErrorBoundary from './components/common/EnhancedErrorBoundary';

// Import AppLayout
import AppLayout from './components/layout/AppLayout';

// Use createRoot API for React 18
const root = createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <EnhancedErrorBoundary>
        <ThemeProvider initialTheme="system">
          <AppLayout>
            <App />
          </AppLayout>
        </ThemeProvider>
      </EnhancedErrorBoundary>
    </Provider>
  </React.StrictMode>
);
